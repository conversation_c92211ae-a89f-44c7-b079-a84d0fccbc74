import cv2
import numpy as np

# 默认阈值
default_lower_blue = np.array([100, 150, 0])
default_upper_blue = np.array([140, 255, 255])

# 全局变量存储当前阈值
current_lower = default_lower_blue.copy()
current_upper = default_upper_blue.copy()

def read_thresholds():
    """读取保存的阈值"""
    try:
        with open("thresholds.txt", "r") as f:
            lines = f.readlines()
            lower = np.array([int(x) for x in lines[0].strip().split(",")])
            upper = np.array([int(x) for x in lines[1].strip().split(",")])
            return lower, upper
    except FileNotFoundError:
        return default_lower_blue, default_upper_blue

def save_thresholds(lower, upper):
    """保存阈值到文件"""
    with open("thresholds.txt", "w") as f:
        f.write(",".join(map(str, lower)) + "\n")
        f.write(",".join(map(str, upper)) + "\n")
    print("阈值已保存到 thresholds.txt")

def nothing(val):
    """滑动条回调函数"""
    pass

def create_trackbars():
    """创建调节滑动条"""
    cv2.namedWindow('Controls')
    
    # 读取保存的阈值
    lower, upper = read_thresholds()
    
    # 创建滑动条
    cv2.createTrackbar('Lower H', 'Controls', lower[0], 179, nothing)
    cv2.createTrackbar('Lower S', 'Controls', lower[1], 255, nothing)
    cv2.createTrackbar('Lower V', 'Controls', lower[2], 255, nothing)
    cv2.createTrackbar('Upper H', 'Controls', upper[0], 179, nothing)
    cv2.createTrackbar('Upper S', 'Controls', upper[1], 255, nothing)
    cv2.createTrackbar('Upper V', 'Controls', upper[2], 255, nothing)

def get_trackbar_values():
    """获取滑动条当前值"""
    lower_h = cv2.getTrackbarPos('Lower H', 'Controls')
    lower_s = cv2.getTrackbarPos('Lower S', 'Controls')
    lower_v = cv2.getTrackbarPos('Lower V', 'Controls')
    upper_h = cv2.getTrackbarPos('Upper H', 'Controls')
    upper_s = cv2.getTrackbarPos('Upper S', 'Controls')
    upper_v = cv2.getTrackbarPos('Upper V', 'Controls')
    
    lower = np.array([lower_h, lower_s, lower_v])
    upper = np.array([upper_h, upper_s, upper_v])
    
    return lower, upper

def main():
    """主函数"""
    # 初始化摄像头
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("无法打开摄像头")
        return
    
    # 创建滑动条
    create_trackbars()
    
    print("颜色阈值调节器")
    print("操作说明:")
    print("- 调节滑动条来改变HSV阈值")
    print("- 按 's' 保存当前阈值")
    print("- 按 'r' 重置为默认阈值")
    print("- 按 'q' 或 ESC 退出")
    
    while True:
        # 读取摄像头帧
        ret, frame = cap.read()
        if not ret:
            print("无法读取摄像头")
            break
        
        # 转换为HSV色彩空间
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 获取当前滑动条值
        lower, upper = get_trackbar_values()
        
        # 创建掩膜
        mask = cv2.inRange(hsv, lower, upper)
        
        # 应用掩膜
        result = cv2.bitwise_and(frame, frame, mask=mask)
        
        # 在原图上显示当前阈值
        text_lower = f"Lower: H={lower[0]} S={lower[1]} V={lower[2]}"
        text_upper = f"Upper: H={upper[0]} S={upper[1]} V={upper[2]}"
        
        cv2.putText(frame, text_lower, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        cv2.putText(frame, text_upper, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        cv2.putText(frame, "Press 's' to save, 'r' to reset, 'q' to quit", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示图像
        cv2.imshow('Original', frame)
        cv2.imshow('Mask', mask)
        cv2.imshow('Result', result)
        
        # 键盘事件处理
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('q') or key == 27:  # 'q' 或 ESC 退出
            break
        elif key == ord('s'):  # 's' 保存阈值
            save_thresholds(lower, upper)
        elif key == ord('r'):  # 'r' 重置阈值
            cv2.setTrackbarPos('Lower H', 'Controls', default_lower_blue[0])
            cv2.setTrackbarPos('Lower S', 'Controls', default_lower_blue[1])
            cv2.setTrackbarPos('Lower V', 'Controls', default_lower_blue[2])
            cv2.setTrackbarPos('Upper H', 'Controls', default_upper_blue[0])
            cv2.setTrackbarPos('Upper S', 'Controls', default_upper_blue[1])
            cv2.setTrackbarPos('Upper V', 'Controls', default_upper_blue[2])
            print("阈值已重置为默认值")
    
    # 清理资源
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
