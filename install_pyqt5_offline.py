#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt5离线安装助手
"""

import sys
import platform
import subprocess
import os

def get_system_info():
    """获取系统信息"""
    print("系统信息:")
    print(f"  Python版本: {sys.version}")
    print(f"  Python位数: {platform.architecture()[0]}")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  处理器: {platform.processor()}")
    
    # 确定需要的wheel文件
    python_version = f"{sys.version_info.major}{sys.version_info.minor}"
    architecture = "win_amd64" if platform.architecture()[0] == "64bit" else "win32"
    
    wheel_name = f"PyQt5-5.15.7-cp{python_version}-cp{python_version}m-{architecture}.whl"
    
    print(f"\n推荐的PyQt5版本: {wheel_name}")
    
    return wheel_name

def check_pyqt5():
    """检查PyQt5是否已安装"""
    try:
        import PyQt5
        from PyQt5.QtCore import QT_VERSION_STR
        print(f"PyQt5已安装，版本: {QT_VERSION_STR}")
        return True
    except ImportError:
        print("PyQt5未安装")
        return False

def install_with_conda():
    """尝试使用conda安装"""
    print("\n尝试使用conda安装PyQt5...")
    try:
        result = subprocess.run(
            ["conda", "install", "pyqt", "-c", "conda-forge", "-y"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✓ conda安装成功")
            return True
        else:
            print(f"✗ conda安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ conda安装超时")
        return False
    except FileNotFoundError:
        print("✗ 未找到conda命令")
        return False
    except Exception as e:
        print(f"✗ conda安装出错: {e}")
        return False

def install_with_pip_offline():
    """提供pip离线安装指导"""
    wheel_name = get_system_info()
    
    print(f"\n如果conda安装失败，请手动下载PyQt5:")
    print(f"1. 访问: https://pypi.org/project/PyQt5/#files")
    print(f"2. 下载文件: {wheel_name}")
    print(f"3. 将文件保存到: d:\\视觉\\code\\")
    print(f"4. 运行命令: pip install d:\\视觉\\code\\{wheel_name}")
    
    # 检查是否已下载
    wheel_path = f"d:\\视觉\\code\\{wheel_name}"
    if os.path.exists(wheel_path):
        print(f"\n发现下载的文件: {wheel_path}")
        try:
            result = subprocess.run(
                ["pip", "install", wheel_path],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✓ 离线安装成功")
                return True
            else:
                print(f"✗ 离线安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"✗ 离线安装出错: {e}")
            return False
    
    return False

def main():
    """主函数"""
    print("PyQt5安装助手")
    print("=" * 40)
    
    # 检查当前状态
    if check_pyqt5():
        print("PyQt5已正确安装，无需重新安装")
        return
    
    # 尝试conda安装
    if install_with_conda():
        if check_pyqt5():
            print("\n✓ PyQt5安装成功！")
            return
    
    # 提供离线安装指导
    install_with_pip_offline()
    
    # 最终检查
    if check_pyqt5():
        print("\n✓ PyQt5安装成功！")
    else:
        print("\n✗ PyQt5安装失败，请尝试使用OpenCV版本的程序")

if __name__ == "__main__":
    main()
