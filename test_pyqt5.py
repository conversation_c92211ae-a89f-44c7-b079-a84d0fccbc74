import sys
from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout

def test_pyqt5():
    """测试PyQt5是否正常工作"""
    try:
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QWidget()
        window.setWindowTitle('PyQt5 测试')
        window.setGeometry(100, 100, 300, 200)
        
        # 创建布局和标签
        layout = QVBoxLayout()
        label = QLabel('PyQt5 配置成功！')
        layout.addWidget(label)
        
        window.setLayout(layout)
        window.show()
        
        print("PyQt5 配置成功！窗口已显示")
        print("请关闭窗口来结束测试")
        
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"PyQt5 导入失败: {e}")
    except Exception as e:
        print(f"PyQt5 运行错误: {e}")

if __name__ == "__main__":
    test_pyqt5()
