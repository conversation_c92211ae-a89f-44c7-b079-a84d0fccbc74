#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt5完整安装测试
"""

import sys
import traceback

def test_pyqt5_complete():
    """完整测试PyQt5安装"""
    
    print("=" * 50)
    print("PyQt5 完整安装测试")
    print("=" * 50)
    
    # 测试1：基础导入
    print("\n1. 测试基础导入...")
    try:
        import PyQt5
        print("✓ PyQt5 基础模块导入成功")
        print(f"  安装路径: {PyQt5.__file__}")
    except Exception as e:
        print(f"✗ PyQt5 基础模块导入失败: {e}")
        return False
    
    # 测试2：核心模块
    print("\n2. 测试核心模块...")
    try:
        from PyQt5.QtCore import QT_VERSION_STR, PYQT_VERSION_STR, Qt
        print("✓ PyQt5.QtCore 导入成功")
        print(f"  Qt版本: {QT_VERSION_STR}")
        print(f"  PyQt5版本: {PYQT_VERSION_STR}")
    except Exception as e:
        print(f"✗ PyQt5.QtCore 导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试3：GUI模块
    print("\n3. 测试GUI模块...")
    try:
        from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, 
                                   QLabel, QVBoxLayout, QHBoxLayout, 
                                   QPushButton, QSlider)
        from PyQt5.QtGui import QImage, QPixmap
        print("✓ PyQt5.QtWidgets 和 PyQt5.QtGui 导入成功")
    except Exception as e:
        print(f"✗ PyQt5 GUI模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试4：创建应用程序
    print("\n4. 测试创建应用程序...")
    try:
        app = QApplication(sys.argv)
        print("✓ QApplication 创建成功")
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle('PyQt5 安装测试')
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中心部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加标签
        label = QLabel('PyQt5 安装成功！')
        layout.addWidget(label)
        
        # 添加按钮
        button = QPushButton('测试按钮')
        layout.addWidget(button)
        
        # 添加滑动条
        slider = QSlider(Qt.Horizontal)
        slider.setRange(0, 100)
        slider.setValue(50)
        layout.addWidget(slider)
        
        # 显示窗口
        window.show()
        
        print("✓ 测试窗口创建成功")
        print("\n" + "=" * 50)
        print("所有测试通过！PyQt5安装完全成功！")
        print("请关闭窗口来结束测试...")
        print("=" * 50)
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ 创建应用程序失败: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    test_pyqt5_complete()
