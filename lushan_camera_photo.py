#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
庐山派K230按键拍照程序
基于庐山派K230官方例程修改
"""

import time, os, sys
import utime
from media.sensor import *
from media.display import *
from media.media import *
from machine import Pin, FPIOA

# 修改照片存储路径
PHOTO_DIR = '/sdcard/photo'

# 庐山派K230的FPIOA引脚配置
fpioa = FPIOA()
fpioa.set_function(62, FPIOA.GPIO62)  # LED引脚
fpioa.set_function(53, FPIOA.GPIO53)  # KEY引脚

# 初始化LED和按键
LED = Pin(62, Pin.OUT)  # LED初始熄灭
KEY = Pin(53, Pin.IN, Pin.PULL_DOWN)  # 按键使用下拉电阻

def ensure_photo_dir():
    """确保照片存储目录存在"""
    try:
        # 首先检查/sdcard目录是否存在
        if not 'sdcard' in os.listdir('/'):
            print("未找到sdcard目录")
            return False

        # 确保photo目录存在
        if not 'photo' in os.listdir('/sdcard'):
            try:
                os.mkdir(PHOTO_DIR)
                print(f"成功创建目录: {PHOTO_DIR}")
            except Exception as e:
                print(f"创建photo目录失败: {e}")
                return False
        return True
    except Exception as e:
        print(f"检查目录失败: {e}")
        return False

def get_next_photo_number():
    """获取下一个照片编号"""
    try:
        files = os.listdir(PHOTO_DIR)
        # 过滤出.jpg文件
        jpg_files = [f for f in files if f.endswith('.jpg')]
        if not jpg_files:
            return 0
        # 提取所有文件名中的数字
        numbers = [int(f.split('.')[0]) for f in jpg_files]
        # 返回最大数字+1
        return max(numbers) + 1
    except:
        return 0

def get_current_max_photo():
    """获取当前最大的照片编号"""
    try:
        files = os.listdir(PHOTO_DIR)
        jpg_files = [f for f in files if f.endswith('.jpg')]
        if not jpg_files:
            return "无照片"
        numbers = [int(f.split('.')[0]) for f in jpg_files]
        return f"{max(numbers)}.jpg"
    except:
        return "读取错误"

def save_image(img, photo_number):
    """保存图片"""
    try:
        photo_path = f"{PHOTO_DIR}/{photo_number}.jpg"
        # 使用compress方法压缩并保存
        compressed_data = img.compress(quality=95)
        with open(photo_path, "wb") as f:
            f.write(compressed_data)
        return True
    except Exception as e:
        print(f"保存图片失败: {e}")
        return False

# 在try块外初始化sensor为None
sensor = None

try:
    # 确保photo文件夹存在
    if not ensure_photo_dir():
        raise Exception("初始化失败")

    # 获取下一个照片编号
    photo_number = get_next_photo_number()

    # 庐山派K230摄像头初始化（使用默认CSI2接口）
    sensor = Sensor(id=2)  # 庐山派默认摄像头接口为CSI2
    # 重置摄像头sensor
    sensor.reset()

    # 设置帧大小为800x480（适配庐山派LCD屏幕）
    sensor.set_framesize(width=800, height=480)
    # 设置通道0的输出像素格式为RGB565
    sensor.set_pixformat(Sensor.RGB565)

    # 庐山派K230显示屏初始化
    Display.init(Display.ST7701, width=800, height=480, to_ide=True)

    # 初始化媒体管理器
    MediaManager.init()
    # 启动传感器
    sensor.run()

    # 添加时钟对象用于计算帧率
    clock = time.clock()

    print("庐山派K230按键拍照程序启动成功")
    print("按下按键进行拍照...")

    while True:
        os.exitpoint()
        clock.tick()  # 更新时钟

        # 捕获通道0的图像
        original_img = sensor.snapshot()  # 保存用的原始图像
        display_img = original_img.copy()  # 显示用的图像副本

        # 在右上角显示当前最大图片名称和帧率（只在显示图像上绘制）
        current_max = get_current_max_photo()
        # 使用绿色显示文字 (R=0, G=255, B=0)
        display_img.draw_string_advanced(400, 20, 30, f"最新: {current_max}", color=(0, 255, 0))
        # 显示帧率
        display_img.draw_string_advanced(400, 60, 30, f"FPS: {clock.fps():.1f}", color=(0, 255, 0))

        # 打印帧率到控制台
        print(f"FPS: {clock.fps():.1f}")

        # 检测按键（庐山派按键按下为高电平）
        if KEY.value() == 1:  # 按键按下
            time.sleep_ms(10)  # 消除抖动
            if KEY.value() == 1:  # 确认按键被按下
                print("检测到按键按下")
                LED.value(1)  # 拍照时点亮LED

                # 保存原始图片（不含文字）
                if save_image(original_img, photo_number):
                    print(f"已保存照片: {photo_number}.jpg")
                    photo_number += 1

                LED.value(0)  # 拍照完成后熄灭LED

                # 等待按键松开
                while KEY.value():
                    pass

        Display.show_image(display_img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 停止传感器运行
    if sensor:
        sensor.stop()
    # 反初始化显示模块
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    # 释放媒体缓冲区
    MediaManager.deinit()
    # 确保LED熄灭
    LED.value(0)
