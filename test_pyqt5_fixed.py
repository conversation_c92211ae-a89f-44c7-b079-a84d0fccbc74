#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_pyqt5_installation():
    """测试PyQt5安装是否完整"""
    
    print("=== PyQt5 安装检查 ===")
    
    # 测试核心模块
    try:
        from PyQt5.QtCore import QT_VERSION_STR, PYQT_VERSION_STR
        print(f"✓ PyQt5.QtCore 导入成功")
        print(f"  Qt版本: {QT_VERSION_STR}")
        print(f"  PyQt5版本: {PYQT_VERSION_STR}")
    except ImportError as e:
        print(f"✗ PyQt5.QtCore 导入失败: {e}")
        return False
    
    # 测试Widgets模块
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel
        print(f"✓ PyQt5.QtWidgets 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5.QtWidgets 导入失败: {e}")
        return False
    
    # 测试GUI模块
    try:
        from PyQt5.QtGui import QImage, QPixmap
        print(f"✓ PyQt5.QtGui 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5.QtGui 导入失败: {e}")
        return False
    
    print("\n=== 创建测试窗口 ===")
    
    # 测试创建应用程序
    try:
        import sys
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle('PyQt5 配置测试')
        window.setGeometry(100, 100, 300, 100)
        
        label = QLabel('PyQt5 配置成功！')
        
        # 简单布局
        from PyQt5.QtWidgets import QVBoxLayout
        layout = QVBoxLayout()
        layout.addWidget(label)
        window.setLayout(layout)
        
        window.show()
        
        print("✓ 测试窗口创建成功")
        print("请关闭窗口来结束测试...")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ 创建测试窗口失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_pyqt5_installation()
